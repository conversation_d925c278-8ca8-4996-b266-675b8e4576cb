---
alwaysApply: true
---
# AEC-FT 车规芯片终测智能调度平台 - 项目开发规则

## 📋 规则说明

**创建时间**: 2025-01-18  
**版本**: 1.0  
**适用范围**: 所有开发、设计、测试活动  
**执行要求**: **强制遵循，任何开发活动前必须先检查此规则**

---

## 🎯 核心原则

1. **稳定可靠**: 系统稳定性是第一优先级，所有变更必须考虑对现有功能的影响
2. **配置化管理**: 避免硬编码，使用统一配置管理系统 (config.ini > 环境变量 > 默认值)
3. **性能优先**: 使用缓存、连接池、优化查询等技术确保高性能
4. **安全第一**: 实现RBAC权限控制，防止SQL注入等安全漏洞
5. **向后兼容**: 保持API和数据结构的向后兼容性

---

## 🏗️ 技术栈规范 (强制遵循)

### 后端技术栈
- **Python**: 3.8+ (推荐 3.11+)
- **Web框架**: Flask 2.3.3 + Flask-SocketIO 5.3.5
- **数据库**: MySQL 8.0+ (主数据库) + Redis 4.0+ (缓存)
- **ORM**: Flask-SQLAlchemy 3.0.5
- **任务调度**: APScheduler 3.10.4
- **数据处理**: pandas 2.2.1 + openpyxl 3.1.2

### 前端技术栈
- **基础**: HTML5 + CSS3 (CSS变量) + JavaScript (ES6+)
- **UI框架**: Bootstrap 5 (紧凑布局，信息密度高)
- **API客户端**: UnifiedAPIClient (统一缓存控制)
- **图表**: ECharts (数据可视化)
- **表格**: DataTables (增强功能)
- **图标**: Font Awesome (内部引用)
- **实时通信**: Socket.IO Client

### 禁止使用
- ❌ SQLite作为业务数据库 (除日志/缓存场景)
- ❌ 外部CDN依赖 (影响性能)
- ❌ 硬编码配置 (必须使用配置文件)

---

## 📐 架构设计规范

### API版本管理
- **API v1**: 已废弃，仅保留兼容性支持
- **API v2**: 主要开发版本，路径格式 `/api/v2/module/action`
- **新功能**: 必须在API v2或更高版本实现

### 蓝图组织结构
```
app/
├── api_v2/           # API v2主要开发
│   ├── auth/         # 认证模块
│   ├── production/   # 生产调度模块
│   ├── orders/       # 订单管理模块
│   ├── resources/    # 资源管理模块
│   └── system/       # 系统管理模块
├── services/         # 业务服务层
├── models/           # 数据模型层
├── utils/            # 工具层
└── templates/        # 前端模板
```

### 服务层设计
- **DataSourceManager**: 数据源管理
- **RealSchedulingService**: 智能排产算法
- **UniversalExcelParser**: 通用Excel解析
- **CacheManager**: 多级缓存管理
- **TaskManager**: 任务调度管理

---

## 🔧 开发流程规范

### 新增页面/菜单流程 (强制)
1. **前端开发**: 在 `app/templates/` 创建模板，遵循现有结构
2. **后端开发**: 在 `app/api_v2/` 开发API (优先v2)
3. **菜单配置**: 更新 `app/config/menu_config.py`
   - 添加菜单ID到 `MENU_ID_MAP`
   - 在 `MENU_CONFIG` 定义菜单结构
4. **权限配置**: 在数据库 `aps.user_permissions` 配置权限
5. **缓存清理**: 调用 `/clear_cache` 清理缓存
6. **测试验证**: 验证访问权限和功能

### 配置管理流程
- **开发环境**: 可使用默认配置或环境变量
- **生产环境**: 必须使用 `config.ini` 配置文件
- **配置优先级**: config.ini > 环境变量 > 默认值

### 数据库操作流程
- **初始化**: `python run.py init-db`
- **迁移**: `python run.py migrate`
- **表名规范**: 严格区分大小写，与现有表名一致

---

## 🎨 前端设计规范

### UI/UX标准
- **布局**: Bootstrap网格系统，紧凑布局，侧边栏200px
- **主题**: 统一主题色 `--theme-color: #b72424`
- **表格**: DataTables增强，支持行内编辑和CRUD
- **图标**: Font Awesome，内部引用优化性能
- **响应式**: 支持桌面端和移动端

### 性能优化
- **资源预加载**: 关键CSS预加载
- **缓存控制**: 使用UnifiedAPIClient统一缓存策略
- **实时通信**: WebSocket支持进度推送和状态更新

### 代码规范
- **关注点分离**: HTML/CSS/JS分离
- **模块化**: 复杂组件拆分独立模块
- **命名规范**: BEM或类似CSS命名规范

---

## 🔒 后端设计规范

### API设计标准
- **统一格式**: `{"status": "success/error", "message": "描述", "data": {...}}`
- **错误处理**: 详细错误信息，便于调试
- **缓存控制**: 适当的缓存控制头
- **实时通信**: WebSocket提供实时推送

### 数据库设计
- **连接池**: 使用 `db_connection_pool.py` 管理连接
- **模型设计**: 优先使用统一模型架构
- **查询优化**: 使用索引，避免N+1查询
- **事务管理**: 合理使用事务确保数据一致性

### 安全规范
- **权限控制**: 基于角色的权限控制(RBAC)
- **SQL注入防护**: 使用ORM或参数化查询
- **输入验证**: 所有用户输入必须验证

---

## 🧮 排产算法规范

### 算法架构
- **主服务**: `RealSchedulingService` 
- **评分维度**: 技术匹配(25%) + 负载均衡(20%) + 交期紧迫(25%) + 产值效率(20%) + 业务优先级(10%)
- **权重配置**: 支持可配置权重，适应不同策略

### 性能要求
- **处理速度**: 1000批次 < 10秒
- **匹配成功率**: > 95%
- **内存使用**: < 1GB

### 算法特性
- **同产品续排**: 减少改机次数
- **智能STAGE匹配**: 容错处理不同命名
- **自动DUE_DATE**: 无交期时自动生成
- **负载追踪**: 实时追踪设备负载

---

## 🧪 测试规范 (强制)

### 6维度测试框架
1. **基础功能测试**: 单元/集成/端到端测试
2. **数据库连接池测试**: 并发/稳定性/异常恢复
3. **真实场景测试**: 真实数据量/用户行为/业务场景
4. **性能测试**: 响应时间/资源使用/压力测试
5. **故障恢复测试**: 服务故障/数据一致性
6. **历史问题回归**: 防止已修复问题重现

### 性能基准要求
- **API响应**: 正常<2秒, 峰值<5秒
- **页面加载**: 首次<3秒, 缓存<1秒
- **数据库查询**: 简单<500ms, 复杂<2秒
- **系统资源**: CPU<80%, 内存<1GB

### 强制检查清单 (20项)
- 基础检查项 (4项): API覆盖/异常测试/错误处理/边界值
- 数据库检查项 (4项): 并发性能/超时重连/事务完整性/稳定性
- 真实场景检查项 (4项): 数据量/用户操作/网络环境/压力表现
- 性能检查项 (4项): 性能基准/资源监控/缓存机制/内存泄漏
- 故障恢复检查项 (4项): 故障场景/数据完整性/自动恢复/人工恢复

---

## ⚠️ 强制执行要求

### 开发前检查
1. **技术选型**: 必须符合技术栈规范
2. **架构设计**: 必须遵循架构设计规范
3. **配置管理**: 必须使用统一配置系统
4. **安全考虑**: 必须考虑安全防护措施

### 开发中检查
1. **代码规范**: 遵循命名和结构规范
2. **性能考虑**: 使用缓存和优化策略
3. **错误处理**: 完善的异常处理机制
4. **日志记录**: 适当的日志级别和内容

### 开发后检查
1. **测试覆盖**: 必须通过6维度测试框架
2. **性能验证**: 必须满足性能基准要求
3. **安全验证**: 必须通过安全检查
4. **文档更新**: 更新相关文档和配置

### 违规处理
- **设计阶段**: 不符合规范的设计方案被退回
- **开发阶段**: 代码审查不通过，影响开发进度
- **测试阶段**: 测试不通过，不允许部署
- **生产阶段**: 可能导致系统故障和用户投诉

---

## 📞 规则执行

**本规则为强制性要求，所有开发活动必须严格遵循。**

**在执行任何开发任务前，必须：**
1. 完整阅读并理解相关规范
2. 对照检查清单逐项验证
3. 确保技术选型符合规范
4. 设计方案符合架构要求

**如有疑问，请参考：**
- 📋 详细文档: `docs/项目需求文档参考/`
- 🔧 配置示例: `config/aps_config.py`
- 📚 API文档: `docs/api_audit/`

---

## 🎯 具体实施指南

### 任务执行前强制检查
**在执行任何开发任务前，必须回答以下问题：**

#### 技术选型检查
- ❓ 使用的技术是否在批准的技术栈列表中？
- ❓ 是否避免了禁止使用的技术？
- ❓ 新增依赖是否经过评估和批准？

#### 架构设计检查
- ❓ API设计是否使用v2版本？
- ❓ 是否遵循了蓝图组织结构？
- ❓ 服务层设计是否合理？

#### 配置管理检查
- ❓ 是否使用了统一配置管理系统？
- ❓ 是否避免了硬编码配置？
- ❓ 配置优先级是否正确？

#### 安全性检查
- ❓ 是否实现了适当的权限控制？
- ❓ 是否防护了SQL注入等安全漏洞？
- ❓ 用户输入是否进行了验证？

#### 性能考虑检查
- ❓ 是否使用了缓存策略？
- ❓ 数据库查询是否优化？
- ❓ 是否考虑了并发性能？

**如果任何一个问题的答案是"否"或"不确定"，则必须先解决相关问题再继续开发。**

### 代码质量标准
- **命名规范**: 使用有意义的变量和函数名
- **注释要求**: 复杂逻辑必须添加注释
- **错误处理**: 所有可能的异常都要处理
- **日志记录**: 关键操作必须记录日志
- **代码复用**: 避免重复代码，提取公共函数

### 部署前检查清单
- [ ] 所有测试用例通过
- [ ] 性能基准满足要求
- [ ] 安全检查通过
- [ ] 配置文件正确
- [ ] 文档已更新
- [ ] 权限配置正确
- [ ] 缓存清理完成

---

## 📚 参考资源

### 核心文档
- `docs/项目需求文档参考/技术栈规则.md`
- `docs/项目需求文档参考/前端设计规则.md`
- `docs/项目需求文档参考/后端设计规则.md`
- `docs/项目需求文档参考/应用流程规则.md`
- `docs/项目需求文档参考/排产算法设计规则.md`

### 配置文件
- `config/aps_config.py` - 统一配置管理
- `app/config/menu_config.py` - 菜单配置
- `requirements.txt` - 依赖包列表

### 示例代码
- `app/services/real_scheduling_service.py` - 排产算法实现
- `app/static/js/unified_api_client.js` - 统一API客户端
- `app/utils/db_connection_pool.py` - 数据库连接池

---

*此规则基于项目实际情况制定，确保开发质量和系统稳定性。任何违反此规则的开发活动都可能导致系统不稳定或功能缺陷。*
