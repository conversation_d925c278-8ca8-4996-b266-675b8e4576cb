#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查设备配置和利用率问题
"""

import pymysql
import sys

def check_equipment_status():
    """检查设备状态和配置"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            print("=" * 60)
            print("🔍 设备配置分析报告")
            print("=" * 60)
            
            # 1. 总设备数量
            cursor.execute("SELECT COUNT(*) as count FROM eqp_status")
            total_count = cursor.fetchone()['count']
            print(f"📊 总设备数量: {total_count} 台")
            
            # 2. 设备状态分布
            cursor.execute("SELECT STATUS, COUNT(*) as count FROM eqp_status GROUP BY STATUS ORDER BY count DESC")
            status_results = cursor.fetchall()
            print(f"\n📈 设备状态分布:")
            for row in status_results:
                status = row['STATUS'] if row['STATUS'] else '空状态'
                count = row['count']
                print(f"   {status}: {count} 台")
            
            # 3. 配置完整性分析
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN DEVICE IS NOT NULL AND DEVICE != '' THEN 1 ELSE 0 END) as has_device,
                    SUM(CASE WHEN STAGE IS NOT NULL AND STAGE != '' THEN 1 ELSE 0 END) as has_stage,
                    SUM(CASE WHEN HANDLER_ID IS NOT NULL AND HANDLER_ID != '' THEN 1 ELSE 0 END) as has_handler_id,
                    SUM(CASE WHEN DEVICE IS NOT NULL AND DEVICE != '' AND STAGE IS NOT NULL AND STAGE != '' THEN 1 ELSE 0 END) as complete_config
                FROM eqp_status
            """)
            config_result = cursor.fetchone()
            
            print(f"\n🔧 配置完整性分析:")
            print(f"   总设备数: {config_result['total']}")
            print(f"   有产品配置(DEVICE): {config_result['has_device']} 台 ({config_result['has_device']/config_result['total']*100:.1f}%)")
            print(f"   有工序配置(STAGE): {config_result['has_stage']} 台 ({config_result['has_stage']/config_result['total']*100:.1f}%)")
            print(f"   有分选机ID(HANDLER_ID): {config_result['has_handler_id']} 台 ({config_result['has_handler_id']/config_result['total']*100:.1f}%)")
            print(f"   配置完整(DEVICE+STAGE): {config_result['complete_config']} 台 ({config_result['complete_config']/config_result['total']*100:.1f}%)")
            
            # 4. 产品类型分布
            cursor.execute("""
                SELECT DEVICE, COUNT(*) as count 
                FROM eqp_status 
                WHERE DEVICE IS NOT NULL AND DEVICE != '' 
                GROUP BY DEVICE 
                ORDER BY count DESC 
                LIMIT 10
            """)
            device_results = cursor.fetchall()
            print(f"\n📦 主要产品类型分布 (Top 10):")
            for row in device_results:
                print(f"   {row['DEVICE']}: {row['count']} 台")
            
            # 5. 工序分布
            cursor.execute("""
                SELECT STAGE, COUNT(*) as count 
                FROM eqp_status 
                WHERE STAGE IS NOT NULL AND STAGE != '' 
                GROUP BY STAGE 
                ORDER BY count DESC
            """)
            stage_results = cursor.fetchall()
            print(f"\n⚙️ 工序分布:")
            for row in stage_results:
                print(f"   {row['STAGE']}: {row['count']} 台")
            
            # 6. 查找排产相关表
            cursor.execute("SHOW TABLES")
            all_tables = [table[0] for table in cursor.fetchall()]

            scheduling_tables = [t for t in all_tables if any(keyword in t.lower() for keyword in ['schedul', 'result', 'assign', 'plan'])]
            print(f"\n📋 发现的排产相关表: {scheduling_tables}")

            # 尝试从可能的表中查找排产记录
            used_count = 0
            for table_name in scheduling_tables:
                try:
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = [col[0] for col in cursor.fetchall()]
                    if 'HANDLER_ID' in columns:
                        cursor.execute(f"""
                            SELECT COUNT(DISTINCT HANDLER_ID) as count
                            FROM {table_name}
                            WHERE HANDLER_ID IS NOT NULL AND HANDLER_ID != ''
                        """)
                        result = cursor.fetchone()
                        if result and result['count'] > used_count:
                            used_count = result['count']
                            print(f"   从表 {table_name} 找到使用的设备: {result['count']} 台")
                except Exception as e:
                    continue

            if used_count > 0:
                print(f"\n📅 排产中实际使用的设备: {used_count} 台")
                print(f"   设备利用率: {used_count}/{total_count} = {used_count/total_count*100:.1f}%")
            else:
                print(f"\n📅 未找到排产使用记录")
            
            print("\n" + "=" * 60)
            print("🎯 问题分析建议:")
            
            complete_ratio = config_result['complete_config'] / config_result['total']
            if complete_ratio < 0.5:
                print("❌ 配置完整性不足50%，这是设备利用率低的主要原因")
                print("   建议：完善设备的DEVICE和STAGE配置信息")
            
            if config_result['has_device'] < config_result['total'] * 0.8:
                print("❌ 超过20%的设备缺少产品配置(DEVICE)")
                print("   建议：为所有设备配置支持的产品类型")
                
            if config_result['has_stage'] < config_result['total'] * 0.8:
                print("❌ 超过20%的设备缺少工序配置(STAGE)")
                print("   建议：为所有设备配置支持的工序类型")
            
            print("=" * 60)
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    check_equipment_status()
